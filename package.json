{"name": "school-management-system", "version": "1.0.0", "main": "dist/server.js", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "nodemon src/server.ts", "build": "rimraf dist && tsc && prisma generate && npm run postbuild", "start": "node dist/server.js", "vercel-build": "tsc --skipLib<PERSON><PERSON><PERSON> --noEmit false --skipD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> true", "postbuild": "node scripts/copy-assets.js", "swagger-docs": "ts-node scripts/swagger-test.ts && echo 'Swagger documentation generated successfully!'", "create-super-manager": "ts-node ./scripts/create-super-manager.ts", "import-students": "ts-node import-students.ts", "prisma:deploy": "prisma migrate deploy", "postinstall": "prisma generate", "seed-periods": "ts-node ./scripts/seed-periods.ts", "start:worker": "ts-node src/workers/reportGenerator.ts"}, "dependencies": {"@faker-js/faker": "^9.5.0", "@prisma/client": "^6.3.1", "bcrypt": "^5.1.1", "bullmq": "^5.49.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^5.1.0", "helmet": "^8.0.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.0", "pg": "^8.7.1", "prisma": "^6.3.1", "puppeteer": "^24.2.0", "rimraf": "^5.0.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "devDependencies": {"@swc/core": "^1.11.5", "@types/bcrypt": "^5.0.2", "@types/bull": "^3.15.9", "@types/cors": "^2.8.17", "@types/ejs": "^3.1.5", "@types/express": "^4.17.21", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.1", "@types/pdfkit": "^0.13.9", "@types/puppeteer": "^5.4.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.9"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} src/seed.ts"}}