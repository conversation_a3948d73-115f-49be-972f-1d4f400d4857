name: <PERSON>uild and Push Docker Image to Docker Hub

on:
  push:
    branches: [ master ] # Trigger on pushes to the master branch
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest # Use the latest Ubuntu runner

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4 # Checks out your code

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3 # Sets up an enhanced Docker builder

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          # Use secrets stored in GitHub repository settings
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image to Docker Hub
        uses: docker/build-push-action@v5
        with:
          context: . # Build context is the root of the repository
          file: ./Dockerfile # Path to your Dockerfile
          push: true # Actually push the image after building
          tags: | # Tags for the image using your Docker Hub repo
            sladeghost/sms-backend:latest
            sladeghost/sms-backend:${{ github.sha }}
          # Optional: Cache Docker layers for faster builds
          cache-from: type=gha
          cache-to: type=gha,mode=max 