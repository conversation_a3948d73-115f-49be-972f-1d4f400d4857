    version: '3.8'

    services:
      sms-backend:
        # --- Use the pre-built image from Docker Hub ---
        image: sladeghost/sms-backend:latest # Tells Docker to pull this image
        # build: .  <-- REMOVE or COMMENT OUT this line
        container_name: sms-backend-app
        restart: unless-stopped
        ports:
          # Map host port 4000 to container port 4000
          - "4000:4000"
        environment:
          # --- CRITICAL: Set these environment variables in Portainer's UI ---
          # Values here are placeholders; Portainer UI values take precedence
          NODE_ENV: production
          PORT: 4000
          DATABASE_URL: "postgresql://neondb_owner:<EMAIL>/sms?sslmode=require" # Placeholder
          DIRECT_URL="postgresql://neondb_owner:<EMAIL>/sms?sslmode=require"
          JWT_SECRET: "thisschoolsappisbest" # Placeholder
          FIRST_SM_EMAIL: "<EMAIL>"
          FIRST_SM_PASSWORD: "SuperManager@123"
          FIRST_SM_USER_ID: "1"
          
          # DIRECT_URL: "YOUR_NEON_DB_URL" # Placeholder - Uncomment if needed
        networks:
          - sms-network

    networks:
      sms-network:
        driver: bridge

    # volumes: # Define the volume if using the DB service
    #   sms-db-data:ng an init container if your setup supports it.
CMD ["sh", "-c", "npx prisma migrate deploy && npm start"]

# --- Alternative CMD if you run migrations manually ---
# CMD ["npm", "start"]
# --- End Alternative CMD ---
