{"compilerOptions": {"sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": false, "strictNullChecks": false, "noImplicitAny": false, "lib": ["ESNext"], "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "ES2020", "module": "CommonJS", "typeRoots": ["./api/*.ts", "./node_modules/@types", "./src/types"]}, "ts-node": {"swc": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}