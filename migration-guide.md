# Fee Management System Migration Guide\n\n## Steps to execute for migration\n\n1. Generate migration:\n   npx prisma migrate dev --name remove_fee_models_update_enrollment\n\n2. Update types (if needed):\n   npx prisma generate\n\n3. Apply migration to production (when ready):\n   npx prisma migrate deploy\n\n## Data Migration\n\nIf yo have existing fee data in SchoolFees and PaymentTransaction tables, you'll need to migrate this data to the new Enrollment schema. Here's a basic approach:\n\n1. For each SchoolFees record:\n   - Update the corresponding Enrollment record with expected_fee\n\n2. For each PaymentTransaction record:\n   - Add the payment amount to the paid_fee field in Enrollment\n   - Add a new entry to payment_logs Json array\n   - Update paid_terms array if term information exists\n   - Recalculate payment_status based on expected and paid amounts\n\nThis can be done through a migration script or directly in your database management tool.
