# Dependency directories
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Production build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# TypeScript cache
*.tsbuildinfo

# IDE specific files
.vscode/
.idea/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db

# Prisma
/prisma/migrations/
/prisma/.env

# Logs
logs
*.log

# PDF reports generated by the application
/reports/*.pdf
/src/reports/*.pdf

# Generated files
/coverage
/tmp

# Keep environment variables out of version control
.env
uploads